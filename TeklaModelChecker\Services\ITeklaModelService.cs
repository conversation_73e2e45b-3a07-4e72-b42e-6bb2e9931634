using System.Collections.Generic;

namespace TeklaModelChecker.Services
{
    public interface ITeklaModelService
    {
        bool IsConnected { get; }
        string ModelName { get; }
        string ModelPath { get; }
        string ModelVersion { get; }

        bool Connect();
        void Disconnect();

        // Placeholder methods for checks
        int CountParts();
        int CountAssemblies();
        int CountBolts();
        int CountWelds();
        int CountReinforcements();

        IEnumerable<string> GetZeroCoordinateObjects();
        IEnumerable<string> GetDuplicateObjects();

        IEnumerable<string> ValidateRequiredAttributes(IEnumerable<string> attributeNames);
        IEnumerable<string> FindAssembliesWithoutMainPart();
        IEnumerable<string> FindPartsWithoutMaterial();
        IEnumerable<string> FindBoltsWithoutHole();

        IEnumerable<(string Severity, string ObjectId, string Message)> GetModelErrors();
    }
}
