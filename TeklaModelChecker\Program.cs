using System;
using System.Windows.Forms;
using TeklaModelChecker.Services;

namespace TeklaModelChecker
{
    internal static class Program
    {
        [STAThread]
        private static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            ITeklaModelService teklaService;

            try
            {
                // Try to use real Tekla service first
                teklaService = new RealTeklaModelService();

                // Test if we can connect to verify Tekla is available
                if (!teklaService.Connect())
                {
                    // If connection fails, show message and fallback to fake service
                    var result = MessageBox.Show(
                        "Cannot connect to Tekla Structures. This could be because:\n\n" +
                        "• Tekla Structures is not running\n" +
                        "• No model is currently open\n" +
                        "• Tekla API is not accessible\n\n" +
                        "Would you like to run in Demo Mode with sample data?",
                        "Tekla Connection Failed",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        teklaService = new FakeTeklaModelService();
                    }
                    else
                    {
                        return; // Exit application
                    }
                }
                else
                {
                    // Successfully connected, disconnect for now (will reconnect when user clicks Connect)
                    teklaService.Disconnect();
                }
            }
            catch (Exception ex)
            {
                // If there's an exception (e.g., Tekla DLLs not found), fallback to fake service
                var result = MessageBox.Show(
                    $"Error initializing Tekla connection:\n{ex.Message}\n\n" +
                    "Would you like to run in Demo Mode with sample data?",
                    "Tekla Initialization Error",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Error);

                if (result == DialogResult.Yes)
                {
                    teklaService = new FakeTeklaModelService();
                }
                else
                {
                    return; // Exit application
                }
            }

            Application.Run(new UI.MainForm(teklaService));
        }
    }
}
