using System;
using System.Collections.Generic;

namespace TeklaModelChecker.Services
{
    public class FakeTeklaModelService : ITeklaModelService
    {
        private readonly Random _rnd = new Random(1234);

        public bool IsConnected { get; private set; }
        public string ModelName { get; private set; } = string.Empty;
        public string ModelPath { get; private set; } = string.Empty;
        public string ModelVersion { get; private set; } = "2024";

        public bool Connect()
        {
            IsConnected = true;
            ModelName = "Demo_Stadium_Project";
            ModelPath = "C:/TeklaModels/Demo_Stadium_Project";
            return true;
        }

        public void Disconnect()
        {
            IsConnected = false;
            ModelName = string.Empty;
            ModelPath = string.Empty;
        }

        public int CountParts() => 1250;
        public int CountAssemblies() => 340;
        public int CountBolts() => 890;
        public int CountWelds() => 210;
        public int CountReinforcements() => 145;

        public IEnumerable<string> GetZeroCoordinateObjects()
        {
            // Simulate none
            yield break;
        }

        public IEnumerable<string> GetDuplicateObjects()
        {
            // Simulate a few duplicates
            yield return "Part ID 10023";
            yield return "Part ID 10024";
        }

        public IEnumerable<string> ValidateRequiredAttributes(IEnumerable<string> attributeNames)
        {
            // Simulate some missing attributes
            yield return "Missing SEGMENT on Part ID 501";
            yield return "Missing PHASE on Part ID 802";
        }

        public IEnumerable<string> FindAssembliesWithoutMainPart()
        {
            yield return "Assembly ID A-100 has no main part";
            yield return "Assembly ID A-245 has no main part";
        }

        public IEnumerable<string> FindPartsWithoutMaterial()
        {
            yield return "Part ID 930 has no material";
        }

        public IEnumerable<string> FindBoltsWithoutHole()
        {
            // Simulate none
            yield break;
        }

        public IEnumerable<(string Severity, string ObjectId, string Message)> GetModelErrors()
        {
            yield return ("Warning", "A-100", "Assembly has conflicting attributes");
            yield return ("Error", "P-930", "Part intersects with main beam");
        }
    }
}

