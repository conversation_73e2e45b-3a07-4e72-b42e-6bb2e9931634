using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using TeklaModelChecker.Services;
using TeklaModelChecker.Checks;

namespace TeklaModelChecker.UI
{
    public partial class MainForm : Form
    {
        private readonly ITeklaModelService _tekla;

        public MainForm(ITeklaModelService tekla)
        {
            _tekla = tekla;
            InitializeComponent();
            UpdateConnectionStatus();
        }

        private void btnConnect_Click(object sender, EventArgs e)
        {
            if (!_tekla.IsConnected)
            {
                _tekla.Connect();
            }
            else
            {
                _tekla.Disconnect();
            }
            UpdateConnectionStatus();
        }

        private async void btnStart_Click(object sender, EventArgs e)
        {
            if (!_tekla.IsConnected)
            {
                AppendResult("Please connect to Tekla first.");
                return;
            }

            progressBar.Value = 0;
            listResults.Items.Clear();

            var steps = new List<Func<Task>>();

            if (chkBasic.Checked)
            {
                steps.Add(async () =>
                {
                    AppendResult("Scanning objects (basic checks)...");
                    await Task.Delay(400);
                    var basic = new BasicChecks(_tekla);
                    var counts = basic.GetCounts();
                    AppendResult($"Parts: {counts.Parts} | Assemblies: {counts.Assemblies} | Bolts: {counts.Bolts} | Welds: {counts.Welds} | Reinforcements: {counts.Reinforcements}");
                });
                steps.Add(async () =>
                {
                    AppendResult("Checking zero coordinate objects...");
                    await Task.Delay(300);
                    var basic = new BasicChecks(_tekla);
                    var zeros = basic.GetZeroCoordinateObjects();
                    AppendResult(zeros.Any() ? $"Found {zeros.Count} zero-position objects" : "No zero-position objects found");
                });
                steps.Add(async () =>
                {
                    AppendResult("Detecting duplicates...");
                    await Task.Delay(300);
                    var basic = new BasicChecks(_tekla);
                    var dups = basic.GetDuplicateObjects();
                    AppendResult(dups.Any() ? $"Detected {dups.Count} duplicate objects" : "No duplicate objects detected");
                });
            }

            if (chkAdvanced.Checked)
            {
                steps.Add(async () =>
                {
                    AppendResult("Checking assemblies without main part...");
                    await Task.Delay(250);
                    var items = _tekla.FindAssembliesWithoutMainPart().ToList();
                    AppendResult(items.Any() ? $"Found {items.Count} assemblies without main part" : "All assemblies have main part");
                });
                steps.Add(async () =>
                {
                    AppendResult("Identifying parts without material...");
                    await Task.Delay(250);
                    var items = _tekla.FindPartsWithoutMaterial().ToList();
                    AppendResult(items.Any() ? $"Found {items.Count} parts without material" : "All parts have materials");
                });
                steps.Add(async () =>
                {
                    AppendResult("Detecting bolts without hole...");
                    await Task.Delay(250);
                    var items = _tekla.FindBoltsWithoutHole().ToList();
                    AppendResult(items.Any() ? $"Found {items.Count} bolts without hole" : "No bolts without holes");
                });
            }

            if (chkAttributes.Checked)
            {
                steps.Add(async () =>
                {
                    AppendResult("Checking required attributes (SEGMENT, PHASE, FABRICATOR, ERECTOR)...");
                    await Task.Delay(300);
                    var issues = _tekla.ValidateRequiredAttributes(new[] { "SEGMENT", "PHASE", "FABRICATOR", "ERECTOR" }).ToList();
                    AppendResult(issues.Any() ? $"Found {issues.Count} attribute issues" : "All required attributes present");
                });
            }

            if (chkErrors.Checked)
            {
                steps.Add(async () =>
                {
                    AppendResult("Scanning model errors...");
                    await Task.Delay(300);
                    var errs = _tekla.GetModelErrors().ToList();
                    AppendResult(errs.Any() ? $"Found {errs.Count} errors/warnings" : "No model errors found");
                });
            }

            int i = 0;
            foreach (var step in steps)
            {
                i++;
                await step();
                progressBar.Value = (int)(100.0 * i / steps.Count);
            }

            AppendResult("Verification complete.");
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            AppendResult("Export to Excel (stub) - ClosedXML will be integrated later.");
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            AppendResult("Print report (stub) - printing integration will be added later.");
        }

        private void UpdateConnectionStatus()
        {
            lblStatus.Text = _tekla.IsConnected ? "Status: ✔ Connected" : "Status: ✖ Not Connected";
            lblModel.Text = _tekla.IsConnected ? $"Model: {_tekla.ModelName} ({_tekla.ModelVersion})" : "Model: -";
            btnConnect.Text = _tekla.IsConnected ? "Disconnect from Tekla" : "Connect to Tekla";
        }

        private void AppendResult(string text)
        {
            listResults.Items.Add(text);
            listResults.TopIndex = listResults.Items.Count - 1;
        }
    }
}

