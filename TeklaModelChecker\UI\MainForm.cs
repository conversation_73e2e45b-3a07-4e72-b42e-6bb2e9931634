using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using TeklaModelChecker.Checks;
using TeklaModelChecker.Services;
using TeklaModelChecker.Checks;

namespace TeklaModelChecker.UI
{
    public partial class MainForm : Form
    {
        private readonly ITeklaModelService _tekla;
        private readonly BasicChecks _basicChecks;

        public MainForm(ITeklaModelService tekla)
        {
            _tekla = tekla;
            _basicChecks = new BasicChecks(tekla);
            InitializeComponent();
            UpdateConnectionStatus();
        }

        private void btnConnect_Click(object sender, EventArgs e)
        {
            if (!_tekla.IsConnected)
            {
                _tekla.Connect();
            }
            else
            {
                _tekla.Disconnect();
            }
            UpdateConnectionStatus();
        }

        private async void btnStart_Click(object sender, EventArgs e)
        {
            if (!_tekla.IsConnected)
            {
                AppendResult("Please connect to <PERSON>kla first.");
                return;
            }

            progressBar.Value = 0;
            listResults.Items.Clear();

            var steps = new List<Func<Task>>();

            if (chkBasic.Checked)
            {
                steps.Add(async () =>
                {
                    AppendResult("Scanning objects (basic checks)...");
                    await Task.Delay(400);
                    foreach (var result in _basicChecks.PerformObjectCounting())
                    {
                        AppendResult($"✔ {result.Description}");
                    }
                });
                steps.Add(async () =>
                {
                    AppendResult("Checking zero coordinate objects...");
                    await Task.Delay(300);
                    foreach (var result in _basicChecks.CheckZeroCoordinates())
                    {
                        var icon = result.ObjectId == "None" ? "✔" : "⚠";
                        AppendResult($"{icon} {result.Description}");
                    }
                });
                steps.Add(async () =>
                {
                    AppendResult("Detecting duplicates...");
                    await Task.Delay(300);
                    foreach (var result in _basicChecks.CheckDuplicateObjects())
                    {
                        var icon = result.ObjectId == "None" ? "✔" : "⚠";
                        AppendResult($"{icon} {result.Description}");
                    }
                });
            }

            if (chkAdvanced.Checked)
            {
                steps.Add(async () =>
                {
                    AppendResult("Checking assemblies without main part...");
                    await Task.Delay(250);
                    var items = _tekla.FindAssembliesWithoutMainPart().ToList();
                    AppendResult(items.Any() ? $"Found {items.Count} assemblies without main part" : "All assemblies have main part");
                });
                steps.Add(async () =>
                {
                    AppendResult("Identifying parts without material...");
                    await Task.Delay(250);
                    var items = _tekla.FindPartsWithoutMaterial().ToList();
                    AppendResult(items.Any() ? $"Found {items.Count} parts without material" : "All parts have materials");
                });
                steps.Add(async () =>
                {
                    AppendResult("Detecting bolts without hole...");
                    await Task.Delay(250);
                    var items = _tekla.FindBoltsWithoutHole().ToList();
                    AppendResult(items.Any() ? $"Found {items.Count} bolts without hole" : "No bolts without holes");
                });
            }

            if (chkAttributes.Checked)
            {
                steps.Add(async () =>
                {
                    AppendResult("Checking required attributes (SEGMENT, PHASE, FABRICATOR, ERECTOR)...");
                    await Task.Delay(300);
                    var issues = _tekla.ValidateRequiredAttributes(new[] { "SEGMENT", "PHASE", "FABRICATOR", "ERECTOR" }).ToList();
                    AppendResult(issues.Any() ? $"Found {issues.Count} attribute issues" : "All required attributes present");
                });
            }

            if (chkErrors.Checked)
            {
                steps.Add(async () =>
                {
                    AppendResult("Scanning model errors...");
                    await Task.Delay(300);
                    var errs = _tekla.GetModelErrors().ToList();
                    AppendResult(errs.Any() ? $"Found {errs.Count} errors/warnings" : "No model errors found");
                });
            }

            int i = 0;
            foreach (var step in steps)
            {
                i++;
                await step();
                progressBar.Value = (int)(100.0 * i / steps.Count);
            }

            AppendResult("Verification complete.");
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            if (listResults.Items.Count == 0)
            {
                AppendResult("No results to export. Please run verification first.");
                return;
            }

            AppendResult("Export to Excel (stub) - ClosedXML will be integrated later.");
            AppendResult($"Would export {listResults.Items.Count} result items to Excel file.");

            // TODO: Implement Excel export using ClosedXML
            // - Create workbook with CheckResult data
            // - Format as table with Category, Issue Type, Object ID, Description columns
            // - Save to user-selected file location
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            if (listResults.Items.Count == 0)
            {
                AppendResult("No results to print. Please run verification first.");
                return;
            }

            AppendResult("Print report (stub) - printing integration will be added later.");
            AppendResult($"Would print report with {listResults.Items.Count} result items.");

            // TODO: Implement print functionality
            // - Format results as printable report
            // - Include model information and timestamp
            // - Show print dialog
        }

        private void UpdateConnectionStatus()
        {
            lblStatus.Text = _tekla.IsConnected ? "Status: ✔ Connected" : "Status: ✖ Not Connected";
            lblModel.Text = _tekla.IsConnected ? $"Model: {_tekla.ModelName} ({_tekla.ModelVersion})" : "Model: -";
            btnConnect.Text = _tekla.IsConnected ? "Disconnect from Tekla" : "Connect to Tekla";
        }

        private void AppendResult(string text)
        {
            listResults.Items.Add(text);
            listResults.TopIndex = listResults.Items.Count - 1;
        }
    }
}

