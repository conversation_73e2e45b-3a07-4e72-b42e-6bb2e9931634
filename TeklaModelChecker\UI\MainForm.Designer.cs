using System.Drawing;
using System.Windows.Forms;

namespace TeklaModelChecker.UI
{
    partial class MainForm
    {
        private Button btnConnect;
        private Label lblStatus;
        private Label lblModel;
        private CheckBox chkBasic;
        private CheckBox chkAdvanced;
        private CheckBox chkAttributes;
        private CheckBox chkErrors;
        private Button btnStart;
        private ProgressBar progressBar;
        private ListBox listResults;
        private Button btnExport;
        private Button btnPrint;

        private void InitializeComponent()
        {
            this.btnConnect = new Button();
            this.lblStatus = new Label();
            this.lblModel = new Label();
            this.chkBasic = new CheckBox();
            this.chkAdvanced = new CheckBox();
            this.chkAttributes = new CheckBox();
            this.chkErrors = new CheckBox();
            this.btnStart = new Button();
            this.progressBar = new ProgressBar();
            this.listResults = new ListBox();
            this.btnExport = new Button();
            this.btnPrint = new Button();
            this.SuspendLayout();
            // 
            // MainForm
            // 
            this.Text = "TEKLA MODEL CHECKER";
            this.ClientSize = new Size(900, 580);
            this.StartPosition = FormStartPosition.CenterScreen;
            // 
            // btnConnect
            // 
            this.btnConnect.Location = new Point(20, 20);
            this.btnConnect.Size = new Size(180, 30);
            this.btnConnect.Text = "Connect to Tekla";
            this.btnConnect.Click += new System.EventHandler(this.btnConnect_Click);
            // 
            // lblStatus
            // 
            this.lblStatus.Location = new Point(220, 24);
            this.lblStatus.AutoSize = true;
            this.lblStatus.Text = "Status: ✖ Not Connected";
            // 
            // lblModel
            // 
            this.lblModel.Location = new Point(220, 48);
            this.lblModel.AutoSize = true;
            this.lblModel.Text = "Model: -";
            // 
            // chkBasic
            // 
            this.chkBasic.Location = new Point(20, 80);
            this.chkBasic.AutoSize = true;
            this.chkBasic.Checked = true;
            this.chkBasic.Text = "Basic Checks";
            // 
            // chkAdvanced
            // 
            this.chkAdvanced.Location = new Point(140, 80);
            this.chkAdvanced.AutoSize = true;
            this.chkAdvanced.Checked = true;
            this.chkAdvanced.Text = "Advanced Checks";
            // 
            // chkAttributes
            // 
            this.chkAttributes.Location = new Point(290, 80);
            this.chkAttributes.AutoSize = true;
            this.chkAttributes.Checked = true;
            this.chkAttributes.Text = "Attributes";
            // 
            // chkErrors
            // 
            this.chkErrors.Location = new Point(390, 80);
            this.chkErrors.AutoSize = true;
            this.chkErrors.Checked = true;
            this.chkErrors.Text = "Error Scanning";
            // 
            // btnStart
            // 
            this.btnStart.Location = new Point(20, 120);
            this.btnStart.Size = new Size(180, 35);
            this.btnStart.Text = "Start Verification";
            this.btnStart.Click += new System.EventHandler(this.btnStart_Click);
            // 
            // progressBar
            // 
            this.progressBar.Location = new Point(220, 125);
            this.progressBar.Size = new Size(650, 23);
            // 
            // listResults
            // 
            this.listResults.Location = new Point(20, 170);
            this.listResults.Size = new Size(850, 320);
            this.listResults.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            // 
            // btnExport
            // 
            this.btnExport.Location = new Point(20, 510);
            this.btnExport.Size = new Size(140, 30);
            this.btnExport.Text = "Export to Excel";
            this.btnExport.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // btnPrint
            // 
            this.btnPrint.Location = new Point(180, 510);
            this.btnPrint.Size = new Size(140, 30);
            this.btnPrint.Text = "Print Report";
            this.btnPrint.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnPrint.Click += new System.EventHandler(this.btnPrint_Click);

            // add controls
            this.Controls.Add(this.btnConnect);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.lblModel);
            this.Controls.Add(this.chkBasic);
            this.Controls.Add(this.chkAdvanced);
            this.Controls.Add(this.chkAttributes);
            this.Controls.Add(this.chkErrors);
            this.Controls.Add(this.btnStart);
            this.Controls.Add(this.progressBar);
            this.Controls.Add(this.listResults);
            this.Controls.Add(this.btnExport);
            this.Controls.Add(this.btnPrint);

            this.ResumeLayout(false);
            this.PerformLayout();
        }
    }
}

