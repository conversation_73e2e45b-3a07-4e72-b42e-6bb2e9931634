using System.Collections.Generic;
using System.Linq;
using TeklaModelChecker.Services;

namespace TeklaModelChecker.Checks
{
    public class BasicCounts
    {
        public int Parts { get; set; }
        public int Assemblies { get; set; }
        public int Bolts { get; set; }
        public int Welds { get; set; }
        public int Reinforcements { get; set; }
    }

    public class BasicChecks
    {
        private readonly ITeklaModelService _tekla;
        public BasicChecks(ITeklaModelService tekla)
        {
            _tekla = tekla;
        }

        public BasicCounts GetCounts()
        {
            return new BasicCounts
            {
                Parts = _tekla.CountParts(),
                Assemblies = _tekla.CountAssemblies(),
                Bolts = _tekla.CountBolts(),
                Welds = _tekla.CountWelds(),
                Reinforcements = _tekla.CountReinforcements(),
            };
        }

        public List<string> GetZeroCoordinateObjects()
        {
            return _tekla.GetZeroCoordinateObjects().ToList();
        }

        public List<string> GetDuplicateObjects()
        {
            return _tekla.GetDuplicateObjects().ToList();
        }
    }
}

