using System.Collections.Generic;
using System.Linq;
using TeklaModelChecker.Models;
using TeklaModelChecker.Services;

namespace TeklaModelChecker.Checks
{
    public class BasicCounts
    {
        public int Parts { get; set; }
        public int Assemblies { get; set; }
        public int Bolts { get; set; }
        public int Welds { get; set; }
        public int Reinforcements { get; set; }
    }

    public class BasicChecks
    {
        private readonly ITeklaModelService _tekla;
        public BasicChecks(ITeklaModelService tekla)
        {
            _tekla = tekla;
        }

        public BasicCounts GetCounts()
        {
            return new BasicCounts
            {
                Parts = _tekla.CountParts(),
                Assemblies = _tekla.CountAssemblies(),
                Bolts = _tekla.CountBolts(),
                Welds = _tekla.CountWelds(),
                Reinforcements = _tekla.CountReinforcements(),
            };
        }

        public List<string> GetZeroCoordinateObjects()
        {
            return _tekla.GetZeroCoordinateObjects().ToList();
        }

        public List<string> GetDuplicateObjects()
        {
            return _tekla.GetDuplicateObjects().ToList();
        }

        public IEnumerable<CheckResult> PerformObjectCounting()
        {
            yield return new CheckResult
            {
                Category = "Basic",
                IssueType = "Object Count",
                ObjectId = "Summary",
                Description = $"Parts: {_tekla.CountParts()}, Assemblies: {_tekla.CountAssemblies()}, Bolts: {_tekla.CountBolts()}, Welds: {_tekla.CountWelds()}, Reinforcements: {_tekla.CountReinforcements()}"
            };
        }

        public IEnumerable<CheckResult> CheckZeroCoordinates()
        {
            var zeroObjects = _tekla.GetZeroCoordinateObjects().ToList();

            if (!zeroObjects.Any())
            {
                yield return new CheckResult
                {
                    Category = "Basic",
                    IssueType = "Zero Coordinates",
                    ObjectId = "None",
                    Description = "No objects found at zero coordinates (0,0,0)"
                };
            }
            else
            {
                foreach (var obj in zeroObjects)
                {
                    yield return new CheckResult
                    {
                        Category = "Basic",
                        IssueType = "Zero Coordinates",
                        ObjectId = obj,
                        Description = "Object located at zero coordinates (0,0,0)"
                    };
                }
            }
        }

        public IEnumerable<CheckResult> CheckDuplicateObjects()
        {
            var duplicates = _tekla.GetDuplicateObjects().ToList();

            if (!duplicates.Any())
            {
                yield return new CheckResult
                {
                    Category = "Basic",
                    IssueType = "Duplicates",
                    ObjectId = "None",
                    Description = "No duplicate objects detected"
                };
            }
            else
            {
                foreach (var dup in duplicates)
                {
                    yield return new CheckResult
                    {
                        Category = "Basic",
                        IssueType = "Duplicates",
                        ObjectId = dup,
                        Description = "Duplicate object detected"
                    };
                }
            }
        }
    }
}

