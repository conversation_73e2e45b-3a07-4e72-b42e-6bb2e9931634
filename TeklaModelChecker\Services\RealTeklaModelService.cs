using System;
using System.Collections.Generic;
using System.Linq;
using Tekla.Structures.Model;

namespace TeklaModelChecker.Services
{
    public class RealTeklaModelService : ITeklaModelService
    {
        private Model _model;
        private ModelObjectEnumerator _modelObjectEnumerator;

        public bool IsConnected { get; private set; }
        public string ModelName { get; private set; } = string.Empty;
        public string ModelPath { get; private set; } = string.Empty;
        public string ModelVersion { get; private set; } = string.Empty;

        public bool Connect()
        {
            try
            {
                _model = new Model();
                
                if (!_model.GetConnectionStatus())
                {
                    IsConnected = false;
                    return false;
                }

                // Get model information
                ModelName = _model.GetInfo().ModelName ?? "Unknown";
                ModelPath = _model.GetInfo().ModelPath ?? "Unknown";
                ModelVersion = _model.GetInfo().ModelVersion ?? "Unknown";
                
                _modelObjectEnumerator = _model.GetModelObjectSelector().GetAllObjects();
                
                IsConnected = true;
                return true;
            }
            catch (Exception)
            {
                IsConnected = false;
                return false;
            }
        }

        public void Disconnect()
        {
            try
            {
                _modelObjectEnumerator?.Dispose();
                _modelObjectEnumerator = null;
                _model = null;
                
                IsConnected = false;
                ModelName = string.Empty;
                ModelPath = string.Empty;
                ModelVersion = string.Empty;
            }
            catch (Exception)
            {
                // Ignore errors during disconnect
            }
        }

        public int CountParts()
        {
            if (!IsConnected) return 0;
            
            try
            {
                var selector = _model.GetModelObjectSelector();
                var parts = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.PART);
                return parts.GetSize();
            }
            catch (Exception)
            {
                return 0;
            }
        }

        public int CountAssemblies()
        {
            if (!IsConnected) return 0;
            
            try
            {
                var selector = _model.GetModelObjectSelector();
                var assemblies = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.ASSEMBLY);
                return assemblies.GetSize();
            }
            catch (Exception)
            {
                return 0;
            }
        }

        public int CountBolts()
        {
            if (!IsConnected) return 0;
            
            try
            {
                var selector = _model.GetModelObjectSelector();
                var bolts = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.BOLT);
                return bolts.GetSize();
            }
            catch (Exception)
            {
                return 0;
            }
        }

        public int CountWelds()
        {
            if (!IsConnected) return 0;
            
            try
            {
                var selector = _model.GetModelObjectSelector();
                var welds = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.WELD);
                return welds.GetSize();
            }
            catch (Exception)
            {
                return 0;
            }
        }

        public int CountReinforcements()
        {
            if (!IsConnected) return 0;
            
            try
            {
                var selector = _model.GetModelObjectSelector();
                var reinforcements = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.REINFORCEMENT);
                return reinforcements.GetSize();
            }
            catch (Exception)
            {
                return 0;
            }
        }

        public IEnumerable<string> GetZeroCoordinateObjects()
        {
            if (!IsConnected) yield break;
            
            try
            {
                var selector = _model.GetModelObjectSelector();
                var parts = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.PART);
                
                while (parts.MoveNext())
                {
                    if (parts.Current is Part part)
                    {
                        var startPoint = part.StartPoint;
                        var endPoint = part.EndPoint;
                        
                        if ((Math.Abs(startPoint.X) < 0.001 && Math.Abs(startPoint.Y) < 0.001 && Math.Abs(startPoint.Z) < 0.001) ||
                            (Math.Abs(endPoint.X) < 0.001 && Math.Abs(endPoint.Y) < 0.001 && Math.Abs(endPoint.Z) < 0.001))
                        {
                            yield return $"Part ID {part.Identifier.ID}";
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Return empty if error occurs
            }
        }

        public IEnumerable<string> GetDuplicateObjects()
        {
            if (!IsConnected) yield break;
            
            try
            {
                var selector = _model.GetModelObjectSelector();
                var parts = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.PART);
                var partPositions = new Dictionary<string, List<int>>();
                
                while (parts.MoveNext())
                {
                    if (parts.Current is Part part)
                    {
                        var positionKey = $"{part.StartPoint.X:F3},{part.StartPoint.Y:F3},{part.StartPoint.Z:F3}-{part.EndPoint.X:F3},{part.EndPoint.Y:F3},{part.EndPoint.Z:F3}";
                        
                        if (!partPositions.ContainsKey(positionKey))
                            partPositions[positionKey] = new List<int>();
                        
                        partPositions[positionKey].Add(part.Identifier.ID);
                    }
                }
                
                foreach (var kvp in partPositions.Where(p => p.Value.Count > 1))
                {
                    foreach (var id in kvp.Value)
                    {
                        yield return $"Part ID {id}";
                    }
                }
            }
            catch (Exception)
            {
                // Return empty if error occurs
            }
        }

        public IEnumerable<string> ValidateRequiredAttributes(IEnumerable<string> attributeNames)
        {
            if (!IsConnected) yield break;

            try
            {
                var selector = _model.GetModelObjectSelector();
                var parts = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.PART);
                var requiredAttribs = attributeNames.ToList();

                while (parts.MoveNext())
                {
                    if (parts.Current is Part part)
                    {
                        foreach (var attribName in requiredAttribs)
                        {
                            string attribValue = "";
                            if (!part.GetUserProperty(attribName, ref attribValue) || string.IsNullOrWhiteSpace(attribValue))
                            {
                                yield return $"Missing {attribName} on Part ID {part.Identifier.ID}";
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Return empty if error occurs
            }
        }

        public IEnumerable<string> FindAssembliesWithoutMainPart()
        {
            if (!IsConnected) yield break;

            try
            {
                var selector = _model.GetModelObjectSelector();
                var assemblies = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.ASSEMBLY);

                while (assemblies.MoveNext())
                {
                    if (assemblies.Current is Assembly assembly)
                    {
                        var mainPart = assembly.GetMainPart();
                        if (mainPart == null)
                        {
                            yield return $"Assembly ID {assembly.Identifier.ID} has no main part";
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Return empty if error occurs
            }
        }

        public IEnumerable<string> FindPartsWithoutMaterial()
        {
            if (!IsConnected) yield break;

            try
            {
                var selector = _model.GetModelObjectSelector();
                var parts = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.PART);

                while (parts.MoveNext())
                {
                    if (parts.Current is Part part)
                    {
                        if (string.IsNullOrWhiteSpace(part.Material.MaterialString))
                        {
                            yield return $"Part ID {part.Identifier.ID} has no material";
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Return empty if error occurs
            }
        }

        public IEnumerable<string> FindBoltsWithoutHole()
        {
            if (!IsConnected) yield break;

            try
            {
                var selector = _model.GetModelObjectSelector();
                var bolts = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.BOLT);

                while (bolts.MoveNext())
                {
                    if (bolts.Current is BoltGroup boltGroup)
                    {
                        // Check if bolt group has holes
                        if (boltGroup.Hole1 == false && boltGroup.Hole2 == false && boltGroup.Hole3 == false && boltGroup.Hole4 == false && boltGroup.Hole5 == false)
                        {
                            yield return $"Bolt ID {boltGroup.Identifier.ID} has no holes";
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Return empty if error occurs
            }
        }

        public IEnumerable<(string Severity, string ObjectId, string Message)> GetModelErrors()
        {
            if (!IsConnected) yield break;

            try
            {
                // Note: Tekla API doesn't have a direct "Model Error Database" access
                // This is a placeholder implementation that checks for common issues

                // Check for parts with invalid geometry
                var selector = _model.GetModelObjectSelector();
                var parts = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.PART);

                while (parts.MoveNext())
                {
                    if (parts.Current is Part part)
                    {
                        // Check for very small parts (potential modeling errors)
                        var distance = Tekla.Structures.Geometry3d.Distance.PointToPoint(part.StartPoint, part.EndPoint);
                        if (distance < 1.0) // Less than 1mm
                        {
                            yield return ("Warning", $"P-{part.Identifier.ID}", "Part has very small length (< 1mm)");
                        }

                        // Check for parts without profile
                        if (string.IsNullOrWhiteSpace(part.Profile.ProfileString))
                        {
                            yield return ("Error", $"P-{part.Identifier.ID}", "Part has no profile defined");
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Return empty if error occurs
            }
        }
    }
}
